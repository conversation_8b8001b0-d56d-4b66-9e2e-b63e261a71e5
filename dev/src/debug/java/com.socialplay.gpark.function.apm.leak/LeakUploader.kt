package com.socialplay.gpark.function.apm.leak

import com.socialplay.gpark.function.download.DownloadFileProvider
import com.socialplay.gpark.function.startup.core.StartupContext
import com.socialplay.gpark.util.DateUtil
import com.socialplay.gpark.util.FileUtil
import shark.ApplicationLeak
import shark.HeapAnalysis
import shark.HeapAnalysisFailure
import shark.HeapAnalysisSuccess
import shark.Leak
import shark.LeakTrace
import shark.LeakTraceObject
import shark.LibraryLeak
import timber.log.Timber
import java.io.File
import java.util.*

/**
 * Created by bo.li
 * Date: 2024/3/26
 * Desc: 保存LeakCanary捕捉到的信息
 */
class LeakUploader {

    fun upload(heapAnalysis: HeapAnalysis) {
        when (heapAnalysis) {
            is HeapAnalysisSuccess -> {
                val allLeakTraces = heapAnalysis
                    .allLeaks
                    .toList()
                    .flatMap { leak ->
                        leak.leakTraces.map { leakTrace -> leak to leakTrace }
                    }
                if (allLeakTraces.isEmpty()) {
                    // Track how often we perform a heap analysis that yields no result.
                    addHeapAnalysis(heapAnalysis)
                } else {
                    record(heapAnalysis)
                    allLeakTraces.forEach { (leak, leakTrace) ->
                        Timber.tag(LeakCanaryConfig.TAG).d("\"Memory leak: ${leak.shortDescription}. See Key tab.\"")

                    }
                }
            }

            is HeapAnalysisFailure -> {
                // Please file any reported failure to
                // https://github.com/square/leakcanary/issues
                Timber.tag(LeakCanaryConfig.TAG).e(heapAnalysis.exception)
            }
        }
    }

    class NoLeakException : RuntimeException()

    private fun addHeapAnalysis(heapAnalysis: HeapAnalysisSuccess) {
        addKey("heapDumpPath", heapAnalysis.heapDumpFile.absolutePath)
        heapAnalysis.metadata.forEach { (key, value) ->
            addKey(key, value)
        }
        addKey("analysisDurationMs", heapAnalysis.analysisDurationMillis)
    }

    private fun addLeak(leak: Leak) {
        addKey("libraryLeak", leak is LibraryLeak)
        if (leak is LibraryLeak) {
            addKey("libraryLeakPattern", leak.pattern.toString())
            addKey("libraryLeakDescription", leak.description)
        }
    }

    private fun addLeakTrace(leakTrace: LeakTrace) {
        addKey("retainedHeapByteSize", leakTrace.retainedHeapByteSize)
        addKey("signature", leakTrace.signature)
        addKey("leakTrace", leakTrace.toString())
    }

    private fun addKey(key: String, value: String) {

    }

    private fun addKey(key: String, value: Int?) {

    }

    private fun addKey(key: String, value: Long) {

    }

    private fun addKey(key: String, value: Boolean) {

    }

    private fun record(heapAnalysis: HeapAnalysisSuccess) {
        val folder = File(DownloadFileProvider.leakCanaryCacheDir, DateUtil.debugFileFormat.format(Date()))
        if (!folder.exists()) {
            folder.mkdirs()
        }
        heapAnalysis.applicationLeaks.forEach {
            val msg = heapAnalysis.getAppMessage(it)
            val fileName = "${StartupContext.get().processType}-APP-${DateUtil.debugFileFormatMs.format(Date())}.txt"
            writeToCache(folder, fileName, msg)
            Timber.tag(LeakCanaryConfig.TAG).d("APPLICATION_LEAK: ${msg}")
        }
        heapAnalysis.libraryLeaks.forEach {
            val msg = heapAnalysis.getLibraryMessage(it)
            val fileName = "${StartupContext.get().processType}-LIB-${DateUtil.debugFileFormatMs.format(Date())}.txt"
            writeToCache(folder, fileName, msg)
            Timber.tag(LeakCanaryConfig.TAG).d("LIBRARY_LEAK: ${msg}")
        }
        heapAnalysis.unreachableObjects.forEach {
            val msg = heapAnalysis.getUnreachableMessage(it)
            val fileName = "${StartupContext.get().processType}-UNREACHABLE_OBJECTS-${
                DateUtil.debugFileFormatMs.format(Date())}.txt"
            writeToCache(folder, fileName, msg)
            Timber.tag(LeakCanaryConfig.TAG).d("UNREACHABLE_OBJECTS: ${msg}")
        }
    }

    private fun writeToCache(folder: File, fileName: String, msg: String) {
        val file = File(folder, fileName)
        FileUtil.writeToFile(file.absolutePath, msg)
    }

    companion object {
        private const val MSG_START = """====================================
HEAP ANALYSIS RESULT
====================================
        """
    }

    private fun HeapAnalysisSuccess.msgFoot() = """METADATA

        Please include this in bug reports and Stack Overflow questions.
        ${
        if (metadata.isNotEmpty()) "\n" + metadata.map { "${it.key}: ${it.value}" }.joinToString(
            "\n"
        ) else ""
    }
        Analysis duration: $analysisDurationMillis ms
        Heap dump file path: ${heapDumpFile.absolutePath}
        Heap dump timestamp: $createdAtTimeMillis
        Heap dump duration: ${if (dumpDurationMillis != HeapAnalysis.DUMP_DURATION_UNKNOWN) "$dumpDurationMillis ms" else "Unknown"}
        ====================================
    """

    private fun HeapAnalysisSuccess.getAppMessage(applicationLeak: ApplicationLeak): String {
        return """$MSG_START

APPLICATION LEAKS

References underlined with "~~~" are likely causes.
Learn more at https://squ.re/leaks.
            
${applicationLeak.toString() + "\n\n"}====================================

${msgFoot()}"""
    }

    private fun HeapAnalysisSuccess.getLibraryMessage(libraryLeak: LibraryLeak): String {
        return """$MSG_START
            
LIBRARY LEAK

A Library Leak is a leak caused by a known bug in 3rd party code that you do not have control over.
See https://square.github.io/leakcanary/fundamentals-how-leakcanary-works/#4-categorizing-leaks

${libraryLeak.toString() + "\n\n"}====================================

${msgFoot()}"""
    }

    private fun HeapAnalysisSuccess.getUnreachableMessage(unreachableObj: LeakTraceObject): String {
        return """$MSG_START

UNREACHABLE OBJECT

An unreachable object is still in memory but LeakCanary could not find a strong reference path
from GC roots.

${unreachableObj.toString() + "\n\n"}====================================

${msgFoot()}"""
    }
}